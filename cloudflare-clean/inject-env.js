// Environment Variables Injection Script
// This script injects environment variables into the HTML at build time

const fs = require('fs');
const path = require('path');

// Environment variables from Cloudflare Pages
// API and WebSocket calls via Cloudflare proxy with Origin CA certificate
const ORACLE_BACKEND = process.env.ORACLE_BACKEND || 'https://voice-chat.vocabu.io';
// WebSocket connects via Cloudflare proxy (without /ws path)
const WS_URL = process.env.WS_URL || 'wss://voice-chat.vocabu.io';

console.log('🔧 Injecting environment variables...');
console.log(`ORACLE_BACKEND: ${ORACLE_BACKEND}`);
console.log(`WS_URL: ${WS_URL}`);

// Read the HTML file
const htmlPath = path.join(__dirname, 'index.html');
let htmlContent = fs.readFileSync(htmlPath, 'utf8');

// Create environment variables script
const envScript = `
<script>
    // Environment variables injected at build time
    // API and WebSocket calls via Cloudflare proxy with valid TLS
    window.ORACLE_BACKEND = '${ORACLE_BACKEND}';
    // WebSocket connects via Cloudflare proxy (without /ws path)
    window.WS_URL = '${WS_URL}';
    console.log('🌍 Environment variables loaded:', {
        ORACLE_BACKEND: window.ORACLE_BACKEND,
        WS_URL: window.WS_URL
    });
</script>`;

// Inject the script before the closing head tag
htmlContent = htmlContent.replace('</head>', `${envScript}\n</head>`);

// Write the updated HTML file
fs.writeFileSync(htmlPath, htmlContent);

console.log('✅ Environment variables injected successfully!');
