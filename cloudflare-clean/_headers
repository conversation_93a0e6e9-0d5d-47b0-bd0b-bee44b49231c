/*
  # Security Headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=()

  # Content Security Policy - Cloudflare proxy with Origin CA certificate
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' wss: https: https://voice-chat.vocabu.io wss://voice-chat.vocabu.io; media-src 'self' blob:; worker-src 'self' blob:;

  # CORS Headers (for API calls)
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization

  # Cache Control
  Cache-Control: public, max-age=3600

/index.html
  # No cache for main page
  Cache-Control: no-cache, no-store, must-revalidate

/assets/*
  # Long cache for static assets
  Cache-Control: public, max-age=31536000, immutable

/*.js
  # Cache JavaScript files
  Cache-Control: public, max-age=86400
  Content-Type: application/javascript

/*.css
  # Cache CSS files
  Cache-Control: public, max-age=86400
  Content-Type: text/css

/*.ico
  # Cache favicon
  Cache-Control: public, max-age=31536000
  Content-Type: image/x-icon
